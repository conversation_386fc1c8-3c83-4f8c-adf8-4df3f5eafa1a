"""
Kafka message processor service.
Handles incoming messages from Kafka topics and processes them.
"""
import logging
import asyncio
from typing import Dict, Any, List

from core.dependencies import Dependencies
from models.data_models import BuildingData, AddressData, MessageType, KafkaMessage
from services.ttl_converter import TTLConverter
from agents.rag_agent import RAGAgent


logger = logging.getLogger(__name__)


class KafkaProcessor:
    """Service for processing Kafka messages."""
    
    def __init__(self, dependencies: Dependencies):
        self.deps = dependencies
        self.ttl_converter = TTLConverter()
        self.rag_agent = RAGAgent(dependencies)
        self._running = False

    async def start(self):
        """Start the Kafka processor."""
        logger.info("Starting Kafka processor...")
        
        # Register message handlers
        self.deps.kafka_client.register_handler("building", self._handle_building_message)
        self.deps.kafka_client.register_handler("address", self._handle_address_message)
        self.deps.kafka_client.register_handler("document", self._handle_document_message)
        
        # Subscribe to topics
        topics = [
            "building_data",
            "address_data", 
            "document_processing"
        ]
        
        await self.deps.kafka_client.subscribe_to_topics(topics)
        
        # Start consuming
        self._running = True
        await self.deps.kafka_client.start_consuming()

    async def stop(self):
        """Stop the Kafka processor."""
        logger.info("Stopping Kafka processor...")
        self._running = False

    async def _handle_building_message(self, topic: str, message: Dict[str, Any]):
        """Handle building data messages."""
        try:
            logger.info(f"Processing building message from topic {topic}")
            
            # Parse building data
            building_data = BuildingData(**message)
            
            # Convert to TTL
            ttl_result = await self.ttl_converter.convert_building_data(building_data)
            
            if ttl_result.success:
                # Upload to GraphDB
                success = await self.deps.graphdb_client.upload_ttl_data(
                    ttl_content=ttl_result.ttl_content,
                    context=f"building:{building_data.building_id}"
                )
                
                if success:
                    logger.info(f"Successfully processed building {building_data.building_id}")
                    
                    # Also add to vector database for search
                    await self._add_to_vector_db(
                        document_id=f"building:{building_data.building_id}",
                        content=self._create_searchable_content(building_data),
                        metadata={
                            "type": "building",
                            "building_id": building_data.building_id,
                            "name": building_data.name,
                            "address": building_data.address
                        }
                    )
                else:
                    logger.error(f"Failed to upload TTL for building {building_data.building_id}")
            else:
                logger.error(f"Failed to convert building data to TTL: {ttl_result.error_message}")
                
        except Exception as e:
            logger.error(f"Error handling building message: {e}")

    async def _handle_address_message(self, topic: str, message: Dict[str, Any]):
        """Handle address data messages."""
        try:
            logger.info(f"Processing address message from topic {topic}")
            
            # Parse address data
            address_data = AddressData(**message)
            
            # Convert to TTL
            ttl_result = await self.ttl_converter.convert_address_data(address_data)
            
            if ttl_result.success:
                # Upload to GraphDB
                success = await self.deps.graphdb_client.upload_ttl_data(
                    ttl_content=ttl_result.ttl_content,
                    context=f"address:{address_data.address_id}"
                )
                
                if success:
                    logger.info(f"Successfully processed address {address_data.address_id}")
                    
                    # Also add to vector database for search
                    await self._add_to_vector_db(
                        document_id=f"address:{address_data.address_id}",
                        content=self._create_searchable_content(address_data),
                        metadata={
                            "type": "address",
                            "address_id": address_data.address_id,
                            "street": address_data.street,
                            "city": address_data.city,
                            "postal_code": address_data.postal_code,
                            "country": address_data.country
                        }
                    )
                else:
                    logger.error(f"Failed to upload TTL for address {address_data.address_id}")
            else:
                logger.error(f"Failed to convert address data to TTL: {ttl_result.error_message}")
                
        except Exception as e:
            logger.error(f"Error handling address message: {e}")

    async def _handle_document_message(self, topic: str, message: Dict[str, Any]):
        """Handle document processing messages."""
        try:
            logger.info(f"Processing document message from topic {topic}")
            
            # Extract document information
            usecase_name = message.get("usecase_name")
            folders = message.get("folders", [])
            files = message.get("files", [])
            
            if not usecase_name or not files:
                logger.warning("Invalid document message: missing usecase_name or files")
                return
            
            # Process each file
            for file_info in files:
                await self._process_document_file(usecase_name, folders, file_info)
                
        except Exception as e:
            logger.error(f"Error handling document message: {e}")

    async def _process_document_file(self, usecase_name: str, folders: List[str], file_info: Dict[str, Any]):
        """Process a single document file."""
        try:
            filename = file_info.get("filename")
            if not filename:
                logger.warning("File info missing filename")
                return
            
            # Construct MinIO object path
            folder_path = "/".join(folders) if folders else ""
            object_path = f"documents/{usecase_name}/{folder_path}/{filename}".replace("//", "/")
            
            logger.info(f"Processing document: {object_path}")
            
            # Check if file exists in MinIO
            exists = await self.deps.minio_client.object_exists(object_path)
            if not exists:
                logger.warning(f"Document not found in MinIO: {object_path}")
                return
            
            # Download file data
            file_data = await self.deps.minio_client.download_data(object_path)
            if not file_data:
                logger.error(f"Failed to download file: {object_path}")
                return
            
            # Process with Docling
            docling_result = await self.deps.docling_service.process_document_from_bytes(
                file_data=file_data,
                filename=filename
            )
            
            if docling_result.success:
                # Save processed content back to MinIO
                await self._save_processed_content(object_path, docling_result)
                
                # Add to vector database for search
                content = docling_result.markdown_content or str(docling_result.json_content)
                if content:
                    await self._add_to_vector_db(
                        document_id=f"doc:{object_path}",
                        content=content,
                        metadata={
                            "type": "document",
                            "usecase_name": usecase_name,
                            "folders": folders,
                            "filename": filename,
                            "object_path": object_path,
                            "processing_metadata": docling_result.metadata
                        }
                    )
                
                logger.info(f"Successfully processed document: {object_path}")
            else:
                logger.error(f"Failed to process document {object_path}: {docling_result.error_message}")
                
        except Exception as e:
            logger.error(f"Error processing document file: {e}")

    async def _save_processed_content(self, original_path: str, docling_result):
        """Save processed content back to MinIO."""
        try:
            base_path = original_path.rsplit(".", 1)[0]  # Remove extension
            
            # Save JSON if available
            if docling_result.json_content:
                json_path = f"{base_path}.json"
                import json
                json_data = json.dumps(docling_result.json_content, indent=2).encode('utf-8')
                await self.deps.minio_client.upload_data(
                    data=json_data,
                    object_name=json_path,
                    content_type="application/json"
                )
                logger.info(f"Saved JSON content: {json_path}")
            
            # Save Markdown if available
            if docling_result.markdown_content:
                md_path = f"{base_path}.md"
                md_data = docling_result.markdown_content.encode('utf-8')
                await self.deps.minio_client.upload_data(
                    data=md_data,
                    object_name=md_path,
                    content_type="text/markdown"
                )
                logger.info(f"Saved Markdown content: {md_path}")
                
        except Exception as e:
            logger.error(f"Error saving processed content: {e}")

    async def _add_to_vector_db(self, document_id: str, content: str, metadata: Dict[str, Any]):
        """Add content to vector database."""
        try:
            success = await self.rag_agent.add_document(
                document_id=document_id,
                content=content,
                metadata=metadata
            )
            
            if success:
                logger.debug(f"Added to vector DB: {document_id}")
            else:
                logger.warning(f"Failed to add to vector DB: {document_id}")
                
        except Exception as e:
            logger.error(f"Error adding to vector DB: {e}")

    def _create_searchable_content(self, data) -> str:
        """Create searchable text content from data objects."""
        if isinstance(data, BuildingData):
            parts = []
            if data.name:
                parts.append(f"Building: {data.name}")
            if data.address:
                parts.append(f"Address: {data.address}")
            if data.coordinates:
                parts.append(f"Coordinates: {data.coordinates}")
            
            # Add properties
            for key, value in data.properties.items():
                parts.append(f"{key}: {value}")
            
            return " | ".join(parts)
            
        elif isinstance(data, AddressData):
            parts = []
            if data.street:
                parts.append(f"Street: {data.street}")
            if data.city:
                parts.append(f"City: {data.city}")
            if data.postal_code:
                parts.append(f"Postal Code: {data.postal_code}")
            if data.country:
                parts.append(f"Country: {data.country}")
            if data.coordinates:
                parts.append(f"Coordinates: {data.coordinates}")
            
            # Add properties
            for key, value in data.properties.items():
                parts.append(f"{key}: {value}")
            
            return " | ".join(parts)
        
        return str(data)
