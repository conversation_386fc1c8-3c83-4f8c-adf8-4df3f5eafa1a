"""
RDF Query Agent for handling SPARQL queries and RDF data operations.
Converts natural language queries to SPARQL and executes them.
"""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from pydantic import BaseModel
from pydantic_ai import Agent, RunContext

from core.dependencies import Dependencies
from models.data_models import QueryResponse


logger = logging.getLogger(__name__)


class RDFQueryContext(BaseModel):
    """Context for the RDF query agent."""
    session_id: Optional[str] = None
    query_text: str
    query_type: str = "natural_language"


class RDFQueryAgent:
    """Agent specialized in RDF queries and SPARQL operations."""
    
    def __init__(self, dependencies: Dependencies):
        self.deps = dependencies
        
        # Create PydanticAI agent
        self.agent = Agent(
            'openai:gpt-4o',
            system_prompt=self._get_system_prompt()
        )
        
        # Register tools
        self._register_tools()

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the RDF query agent."""
        return """
        You are an expert RDF Query Agent specialized in SPARQL queries and RDF data operations.
        
        Your capabilities include:
        1. Converting natural language queries to SPARQL
        2. Executing SPARQL queries against RDF databases
        3. Interpreting and explaining query results
        4. Working with building and address data in RDF format
        
        The RDF database contains:
        - Building data with properties like name, address, coordinates
        - Address data with street, city, postal code, country information
        - Geographic coordinates and spatial relationships
        - Various metadata and properties
        
        Common namespaces you'll work with:
        - building: http://example.org/building/
        - address: http://example.org/address/
        - schema: http://schema.org/
        - geo: http://www.w3.org/2003/01/geo/wgs84_pos#
        
        When converting natural language to SPARQL:
        1. Identify the main entities and relationships
        2. Use appropriate namespaces and predicates
        3. Include LIMIT clauses for large result sets
        4. Use FILTER clauses for specific conditions
        5. Provide clear, well-formatted SPARQL queries
        
        Always explain your SPARQL queries and interpret the results clearly.
        """

    def _register_tools(self):
        """Register tools for the RDF query agent."""
        
        @self.agent.tool
        async def execute_sparql_query(ctx: RunContext[RDFQueryContext], 
                                     sparql_query: str) -> Dict[str, Any]:
            """Execute a SPARQL query against the RDF database."""
            try:
                result = await self.deps.graphdb_client.execute_sparql_query(sparql_query)
                
                # Extract bindings from SPARQL results
                bindings = result.get('results', {}).get('bindings', [])
                
                return {
                    "success": True,
                    "query": sparql_query,
                    "results": bindings,
                    "count": len(bindings)
                }
                
            except Exception as e:
                logger.error(f"Error executing SPARQL query: {e}")
                return {
                    "success": False,
                    "error": str(e),
                    "query": sparql_query
                }

        @self.agent.tool
        async def get_repository_info(ctx: RunContext[RDFQueryContext]) -> Dict[str, Any]:
            """Get information about the RDF repository."""
            try:
                size = await self.deps.graphdb_client.get_repository_size()
                repositories = await self.deps.graphdb_client.get_repositories()
                
                return {
                    "success": True,
                    "repository_size": size,
                    "available_repositories": repositories
                }
                
            except Exception as e:
                logger.error(f"Error getting repository info: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }

        @self.agent.tool
        async def validate_sparql_syntax(ctx: RunContext[RDFQueryContext], 
                                       sparql_query: str) -> Dict[str, Any]:
            """Validate SPARQL query syntax."""
            try:
                # Basic syntax validation - in production you might use a proper SPARQL parser
                query_lower = sparql_query.lower().strip()
                
                # Check for basic SPARQL keywords
                valid_starts = ['select', 'construct', 'ask', 'describe', 'insert', 'delete']
                is_valid = any(query_lower.startswith(start) for start in valid_starts)
                
                # Check for balanced braces
                open_braces = sparql_query.count('{')
                close_braces = sparql_query.count('}')
                balanced_braces = open_braces == close_braces
                
                return {
                    "success": True,
                    "is_valid": is_valid and balanced_braces,
                    "has_valid_start": is_valid,
                    "balanced_braces": balanced_braces,
                    "query": sparql_query
                }
                
            except Exception as e:
                logger.error(f"Error validating SPARQL syntax: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }

        @self.agent.tool
        async def get_common_prefixes(ctx: RunContext[RDFQueryContext]) -> Dict[str, str]:
            """Get common namespace prefixes for SPARQL queries."""
            return {
                "building": "http://example.org/building/",
                "address": "http://example.org/address/",
                "schema": "http://schema.org/",
                "geo": "http://www.w3.org/2003/01/geo/wgs84_pos#",
                "rdf": "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
                "rdfs": "http://www.w3.org/2000/01/rdf-schema#",
                "xsd": "http://www.w3.org/2001/XMLSchema#"
            }

    async def process_query(self, query: str, query_type: str = "natural_language", 
                          session_id: Optional[str] = None) -> Dict[str, Any]:
        """Process a query and return results."""
        try:
            # Create context
            context = RDFQueryContext(
                dependencies=self.deps,
                session_id=session_id,
                query_text=query,
                query_type=query_type
            )
            
            # If it's already a SPARQL query, execute directly
            if query_type == "sparql":
                result = await self.deps.graphdb_client.execute_sparql_query(query)
                bindings = result.get('results', {}).get('bindings', [])
                
                return {
                    "sparql_query": query,
                    "results": bindings,
                    "count": len(bindings),
                    "interpretation": "Direct SPARQL query execution"
                }
            
            # Otherwise, use the agent to convert and execute
            result = await self.agent.run(
                f"Convert this natural language query to SPARQL and execute it: {query}",
                deps=context
            )
            
            return {
                "response": result.output,
                "query_type": query_type,
                "original_query": query
            }
            
        except Exception as e:
            logger.error(f"Error processing RDF query: {e}")
            return {
                "error": str(e),
                "query": query,
                "query_type": query_type
            }

    async def convert_to_sparql(self, natural_language_query: str) -> str:
        """Convert a natural language query to SPARQL."""
        try:
            context = RDFQueryContext(
                dependencies=self.deps,
                query_text=natural_language_query,
                query_type="natural_language"
            )
            
            result = await self.agent.run(
                f"Convert this natural language query to SPARQL (return only the SPARQL query): {natural_language_query}",
                deps=context
            )

            return result.output
            
        except Exception as e:
            logger.error(f"Error converting to SPARQL: {e}")
            raise

    async def explain_query_results(self, sparql_query: str, results: List[Dict[str, Any]]) -> str:
        """Explain SPARQL query results in natural language."""
        try:
            context = RDFQueryContext(
                dependencies=self.deps,
                query_text=sparql_query,
                query_type="sparql"
            )
            
            explanation_prompt = f"""
            Explain these SPARQL query results in natural language:
            
            SPARQL Query:
            {sparql_query}
            
            Results ({len(results)} items):
            {results[:10]}  # Show first 10 results
            
            Provide a clear, human-readable explanation of what the query found.
            """
            
            result = await self.agent.run(explanation_prompt, deps=context)
            return result.output
            
        except Exception as e:
            logger.error(f"Error explaining query results: {e}")
            return f"Found {len(results)} results from the query."
